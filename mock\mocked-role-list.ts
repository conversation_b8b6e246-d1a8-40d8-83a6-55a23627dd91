function getMockData() {
  const data: any[] = [];

  for (let i = 0; i < 100; i++) {
    data.push({
      id: i + 1,
      roleName: `角色 ${i + 1}`,
      orgId: Math.floor(Math.random() * 10) + 1,
      orgName: `Organization ${Math.floor(Math.random() * 10) + 1}`,
      activeFlag: i % 2 === 0,
      description: `Description for Role ${i + 1}`,
      createTime: Date.now() - Math.floor(Math.random() * 1000000000),
      updateTime: Date.now() - Math.floor(Math.random() * 100000000),
    });
  }

  return data;
}

// Example usage
export default getMockData;

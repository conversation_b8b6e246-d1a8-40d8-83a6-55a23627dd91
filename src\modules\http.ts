import axios from 'axios';
import { GetLogger, GetUserInfo } from '../global-state';

const defaultLogger = GetLogger();

const HttpRequestConfig = {
    baseUrl: '/api',
};

const HttpModule = {
    AxiosInstance: CreateAxiosInstance(),
}

function GetGlobalUserInfo() {
    return GetUserInfo();
}

function CreateAxiosInstance() {

    const AxiosInstance = axios.create({
    
        baseURL: HttpRequestConfig.baseUrl,
        withCredentials: false,
        timeout: 1000 * 60,
    });

    AxiosInstance.interceptors.request.use(

        function (config) {
    
            const fixed: any = { appplt: 'web', _ts: new Date().getTime() };
            const params = Object.assign(fixed, config.params || {});
            const usr = GetGlobalUserInfo();

            if (usr) {

                const { userId, orgId, token } = usr;
                Object.assign(params, { user_id: userId, token });
                
                if (params.org_id === undefined) {
                    params.org_id = orgId;
                }
            }
            else {
                Object.assign(params, { user_id: 1 });
            }

            config.params = params;
            return config;
        }, 
    
        function(error) {
            return Promise.reject(error);
        }
    );
    
    AxiosInstance.interceptors.response.use(
    
        function(response) {
            
            // console.log('Response Interceptor:', response);
            return response;
        },
    
        function(error) {
            
            let { request, response, message } = error;
            let { status, code } = request || response;
            let error_msg = '';
    
            if (message == 'Network Error') {
                error_msg = '服务连接错误';
            }
            else if ((message || '').indexOf('timeout') >= 0) {
                error_msg = '服务连接超时';
            }
            else if (code == 'ECONNABORTED') {
                error_msg = '服务连接中断';
            }
            else if (status >= 500) {
                error_msg = `服务器未正确响应请求，错误码 = ${status}`;
            }
            else if (status >= 400) {
                error_msg = `数据请求产生错误，错误码 = ${status}`;
            }
    
            if (error_msg) {
                defaultLogger.error(error_msg, { request, response });
            }
            
            error.message = error_msg || message || null;
            return Promise.reject(error);
        }
    );

    return AxiosInstance;
}

/**
 * 配置http请求
 */
export function ConfigHttpRequest(baseUrl: string) {

    HttpRequestConfig.baseUrl = baseUrl;
    HttpModule.AxiosInstance = CreateAxiosInstance();
}

export default HttpModule;
import type { IpcRendererEvent } from 'electron';
import type { UserLoginResponse } from '../../../xtrade-sdk';
import { Repos } from '../../../xtrade-sdk';

const userRepo = new Repos.UserRepo();
const renderer = window.ipcRenderer;

class LoginService {
  static async login(username: string, password: string, mac: string, os: string, quote = false) {
    console.log('to do user login', { username, password, mac, os, quote });
    return await this.loginViaWs(username, password, mac, os, quote);

    // if (Misc.isElectron()) {
    //   return await this.loginViaNs(username, password, mac, os);
    // } else {
    //   return await this.loginViaWs(username, password, mac, os);
    // }
  }

  /**
   * 通过Native Socket方式登录
   */
  private static async loginViaNs(username: string, password: string, mac: string, os: string) {
    return new Promise<{ trade: UserLoginResponse; quote: UserLoginResponse }>(
      (resolve, reject) => {
        const delayTimer = setTimeout(() => {
          reject(new Error('login timeout'));
        }, 1000 * 20);
        function completed(
          event: IpcRendererEvent,
          trade: UserLoginResponse,
          quote: UserLoginResponse,
        ) {
          clearTimeout(delayTimer);
          resolve({ trade, quote });
        }

        renderer.off('ns-login-completed', completed);
        renderer.on('ns-login-completed', completed);
        renderer.send('ns-login', username, password, mac, os);
      },
    );
  }

  /**
   * 通过Web Socket方式登录
   */
  private static async loginViaWs(
    username: string,
    password: string,
    mac: string,
    os: string,
    quote = false,
  ) {
    const resp = await userRepo.SignIn(username, password, mac, os, quote);
    return resp;
  }

  /**
   * 用户注销
   */
  static async logout() {
    return await userRepo.SignOut();
  }
}

export default LoginService;

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import RiskControlService from '@/api/risk-control';
import type { RiskTemplate } from '../../../../xtrade-sdk/dist';
import { showCustomConfirm } from '@/services/confirm-dialog.service';
import { Misc, Utils } from '@/script';

const riskTemplates = ref<RiskTemplate[]>([]);
const searchKeyword = ref('');
const focused = ref<RiskTemplate | null>(null);
const dialogVisible = ref(false);
const newTemplateName = ref('');

const filteredTemplates = computed(() => {
  const kw = (searchKeyword.value || '').toLowerCase();
  return riskTemplates.value.filter(template =>
    template.riskTemplateName.toLowerCase().includes(kw),
  );
});

const emitter = defineEmits<{
  select: [item: RiskTemplate];
  toggle: [];
}>();

const handleSelect = (target: RiskTemplate) => {
  focused.value = target;
  emitter('select', target);
};

const handleAdd = () => {
  newTemplateName.value = '';
  dialogVisible.value = true;
};

async function handleDelete() {
  const current = focused.value;
  if (!current) {
    return ElMessage.error('请选择要删除的模板');
  }

  const result = await showCustomConfirm({
    title: '删除模板',
    message: `确认删除此模板： ${current.riskTemplateName}？`,
    confirmText: '删除',
  });

  if (result !== true) {
    return;
  }

  Utils.remove(riskTemplates.value, x => x.id == current.id);
  focused.value = riskTemplates.value[0] || null;
}

const handleToggle = () => {
  emitter('toggle');
};

const handleCancel = () => {
  newTemplateName.value = '';
  dialogVisible.value = false;
};

const handleConfirm = () => {
  const existingTemplate = riskTemplates.value.find(
    template => template.riskTemplateName === newTemplateName.value,
  );

  if (existingTemplate) {
    ElMessage.error('该风控模板名称已存在');
    return;
  }

  const user = Misc.getUser()!;
  riskTemplates.value.push({
    id: Math.random(),
    riskTemplateName: newTemplateName.value,
    globalRiskTemplate: false,
    orgId: user.orgId,
    createTime: null,
    createUserId: user.userId,
    createUserName: user.username,
  });

  newTemplateName.value = '';
  dialogVisible.value = false;
};

async function request() {
  riskTemplates.value = await RiskControlService.getTemplates();
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="risk-template-list" p-10>
    <div class="search-box" flex aic gap-12>
      <!-- 搜索框 -->
      <el-input
        v-model.trim="searchKeyword"
        placeholder="搜索模板"
        :suffix-icon="Search"
        clearable
      />

      <!-- 添加按钮 -->
      <el-tooltip placement="top" content="添加模板">
        <i class="iconfont icon-add-new" thb1 @click="handleAdd"></i>
      </el-tooltip>

      <!-- 删除按钮 -->
      <el-tooltip placement="top" content="删除模板">
        <i class="iconfont icon-remove" thb1 @click="handleDelete"></i>
      </el-tooltip>

      <!-- 隐藏显示按钮 -->
      <el-tooltip placement="top" content="隐藏|显示该列表">
        <i class="iconfont icon-exchange" thb1 @click="handleToggle"></i>
      </el-tooltip>
    </div>

    <!-- 风控模板列表 -->

    <div class="templite-list" mt-5>
      <template v-for="(template, idx) in filteredTemplates" :key="idx">
        <div
          class="templite-item"
          :class="{ 'is-active': focused && focused.id == template.id }"
          @click="handleSelect(template)"
          h-40
          p-x-14
          p-y-8
        >
          <span fs-14 fw-400>{{ template.riskTemplateName }}</span>
        </div>
      </template>
    </div>

    <!-- 添加模板对话框 -->
    <el-dialog v-model="dialogVisible" title="添加风控模板" width="300px">
      <el-input v-model.trim="newTemplateName" placeholder="请输入风控模板名称" clearable />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.risk-template-list {
  .templite-list {
    .templite-item {
      &:hover,
      &.is-active {
        background-color: var(--g-block-bg-6);
      }
    }
  }
}
</style>

export interface AnyObject {
  [key: string]: any;
}

export interface MetaDataItem {
  label: string;
  value: number | string;
}

export interface TableColumnBasic {
  title: string;
  datakey: string;
}

export interface TableColumnConfigParamMeta {
  /** 表格名称 */
  name: string;
  /** 全量表格列 */
  columns?: TableColumnBasic[];
  /** 分组全量表格列 */
  groups?: { category: string; columns: TableColumnBasic[] }[];
  /** 勾选的列 */
  selected: string[];
}

export interface TableColumnConfigParam extends TableColumnConfigParamMeta {
  /** 勾选的列 */
  callback: (selected: string[]) => void;
}

export function getEmptyTableColumnConfig() {
  return {
    name: '',
    columns: [],
    groups: [],
    selected: [],
    callback: () => {},
  } as TableColumnConfigParam;
}

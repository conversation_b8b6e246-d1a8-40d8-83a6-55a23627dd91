<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, shallowRef } from 'vue';
import { ElMessage, ElMessageBox, TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { Formatter } from '@/script';
import type { MomRole } from '../../../../xtrade-sdk/dist';
import RoleDialog from './RoleDialog.vue';
import RoleMenuTree from './RoleMenuTree.vue';

// 基础列定义
const columns: ColumnDefinition<MomRole> = [
  { key: 'id', title: '角色ID', width: 100, sortable: true },
  { key: 'roleName', title: '角色名称', width: 100, sortable: true },
  { key: 'orgId', title: '机构ID', width: 100, sortable: true }, // （0表示系统默认）
  { key: 'orgName', title: '机构名称', width: 100, sortable: true }, // （默认system）
  { key: 'activeFlag', title: '是否启用', width: 100, sortable: true },
  { key: 'description', title: '角色描述', width: 300, sortable: true },
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
];

// 行操作
const rowActions: RowAction<MomRole>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const records = shallowRef<MomRole[]>([]);
const visible = shallowRef(false);
const role = shallowRef<MomRole | undefined>();
const selectedRole = shallowRef<MomRole | undefined>();

function handleAdd() {
  visible.value = true;
  role.value = undefined;
}

function editRow(row: MomRole) {
  visible.value = true;
  role.value = row;
}

function deleteRow(row: MomRole) {
  ElMessageBox.confirm(`确认删除角色：${row.roleName}？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const { errorCode, errorMsg } = await AdminService.deleteRole(row.id);
    if (errorCode === 0) {
      ElMessage.success('删除成功');
      request();
    } else {
      ElMessage.error(errorMsg || '删除失败');
    }
  });
}

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = await AdminService.getRoles();
}

function handleRowClick(row: MomRole) {
  selectedRole.value = row;
}

onMounted(() => {
  request();
});
</script>

<template>
  <div w-full flex gap-10>
    <div w="50%">
      <VirtualizedTable
        ref="tableRef"
        :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
        :columns="columns"
        :data="records"
        :row-actions="rowActions"
        :row-action-width="170"
        select
        fixed
        @row-click="handleRowClick"
      >
        <template #actions>
          <div class="actions" flex aic>
            <el-button link size="small" class="typical-text-button">
              <i class="iconfont icon-setting"></i>
              <span>列配置</span>
            </el-button>
            <el-button link size="small" class="typical-text-button">
              <i class="iconfont icon-download"></i>
              <span>下载</span>
            </el-button>
            <el-button type="primary" @click="handleAdd">
              <i class="iconfont icon-add-new" mr-5></i>
              <span>新建角色</span>
            </el-button>
          </div>
        </template>
      </VirtualizedTable>
      <RoleDialog v-model="visible" :role="role" @success="request" />
    </div>
    <RoleMenuTree flex-1 :role="selectedRole" />
  </div>
</template>

<style scoped></style>

<script setup lang="ts">
import { Misc } from '@/script';
import { ref } from 'vue';

const user = ref(Misc.getUser())!;
</script>

<template>
  <div class="home-toolkit" w-310 flex aic gap-20>
    <span flex aic gap-24>
      <i class="iconfont icon-more" thb1></i>
      <i class="iconfont icon-setting" thb1></i>
      <i class="iconfont icon-calendar" thb1></i>
      <i class="iconfont icon-bell" thb1></i>
    </span>
    <span flex aic gap-10>
      <img src="../../assets/image/avatar.png" w-40 h-40 rounded-full />
      <div v-if="user" w-60 overflow-hidden>
        <label block w-full fs-14 fw-600 lh-20 toe color="[--g-text-color-2]">
          {{ user.fullName }}
        </label>
        <label block w-full fw-400 lh-16 toe color="[--g-text-color-3]">{{ user.roleName }}</label>
      </div>
    </span>
  </div>
</template>

<style scoped>
.home-toolkit {
  i {
    font-size: 24px;
  }
}
</style>

<template>
  <div class="form-container w-full h-500">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="typical-form"
      label-position="left"
      label-width="120px"
      w-full
      mt-10
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="产品名称" prop="fundName">
            <el-input v-model.trim="formData.fundName" placeholder="请输入产品名称" clearable />
          </el-form-item>
          <el-form-item label="策略" prop="strategyType">
            <el-select
              v-model.trim="formData.strategyType"
              placeholder="请选择策略"
              filterable
              clearable
            >
              <el-option
                v-for="(item, idx) in StrategyTypes"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="基金性质" prop="fundType">
            <el-radio-group v-model.trim="formData.fundType" class="typical-radio-group" w-full>
              <el-radio v-for="(item, idx) in FundCategories" :key="idx" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="成立日" prop="establishedDay">
            <el-date-picker
              v-model.trim="formData.establishedDay"
              type="date"
              placeholder="选择日期"
            />
          </el-form-item>
          <el-form-item label="基金经理" prop="fundManager">
            <el-input v-model.trim="formData.fundManager" placeholder="请输入名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备案号" prop="amacCode">
            <el-input v-model.trim="formData.amacCode" clearable />
          </el-form-item>
          <el-form-item label="基准" prop="basisReference">
            <el-select
              v-model.trim="formData.basisReference"
              placeholder="请选择基准"
              filterable
              clearable
            >
              <el-option
                v-for="(item, idx) in IndexBenchmarks"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="基金类型" prop="fundType">
            <el-radio-group v-model.trim="formData.fundType" class="typical-radio-group" w-full>
              <el-radio v-for="(item, idx) in FundTypes" :key="idx" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="产品状态" prop="closedFlag">
            <el-radio-group v-model.trim="formData.closedFlag" class="typical-radio-group" w-full>
              <el-radio v-for="(item, idx) in FundStatuses" :key="idx" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="管理机构" prop="fundOrganization">
            <el-input v-model.trim="formData.fundOrganization" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入信息"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="button-container" flex jcc gap-16 pt-16 pb-4>
    <el-button @click="cancel" w-200>取消</el-button>
    <el-button type="primary" @click="save" w-220>保存</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { MakeDefaultProductInfo, type ProductInfo } from '@/types';
import { Utils } from '@/script';
import {
  StrategyTypes,
  FundTypes,
  FundCategories,
  FundStatuses,
  IndexBenchmarks,
} from '@/enum/product';

const contextProduct = defineModel<ProductInfo | null>({});
const sample = MakeDefaultProductInfo();
const rules = {
  fundName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
};

const formData = ref<ProductInfo>(Utils.deepAssign({}, sample));

watch(
  () => contextProduct.value,
  newValue => {
    formData.value = newValue
      ? Utils.deepClone(newValue)
      : Utils.deepAssign<ProductInfo>({}, sample);
  },
  { immediate: true },
);

const cancel = () => {
  // 清空表单数据或重置为默认值
};

const save = () => {
  // if (!formData.value.productName || !formData.value.managementInstitution) {
  //   ElMessage.error('请填写必填项');
  //   return;
  // }
  // 提交表单数据逻辑
};
</script>

<style scoped></style>

<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { Formatter } from '@/script';
import type { MomTerminal } from '../../../../xtrade-sdk/dist';

// 基础列定义
const columns: ColumnDefinition<MomTerminal> = [
  { key: 'id', title: '终端ID', width: 100, sortable: true },
  { key: 'terminalName', title: '终端名称', width: 100, sortable: true },
  { key: 'pwd', title: '密码', width: 100, sortable: true },
  { key: 'description', title: '描述信息', width: 300, sortable: true },
  { key: 'status', title: '状态', width: 100, sortable: true }, // （1启用，0禁用）
  { key: 'interfaceType', title: '接口类型', width: 100, sortable: true }, // （期货/股票类型的终端）
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
];

// 行操作
const rowActions: RowAction<MomTerminal>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function editRow(row: MomTerminal) {
  console.log('edit', row);
}

function deleteRow(row: MomTerminal) {
  console.log('delete', row);
}

const records = shallowRef<MomTerminal[]>([]);
const tableRef = useTemplateRef('tableRef');

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = await AdminService.getTerminals();
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button>
        <el-button type="primary">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建交易终端</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>

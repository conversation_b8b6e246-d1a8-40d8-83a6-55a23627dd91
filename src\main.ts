import './assets/style/main.css';
import 'virtual:uno.css';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';

import App from './App.vue';
import router from './router';
import { Misc } from './script';
import { LoginService } from './api';
import { getConfigedServers, getMacAddress, getOsInfo } from '../shared/servers';

import {
  GlobalState,
  RuntimeEnvironment,
  HttpAssist,
  ServerManager,
  LogLevel,
} from '../../xtrade-sdk';

// 设置日志级别
const loggers = GlobalState.GetLoggers();
loggers.forEach(item => item.setLogLevel(LogLevel.DEBUG));

// 获取配置可用的服务器
const Servers = getConfigedServers();
const Mac = getMacAddress();
const Os = getOsInfo();

/**
 * 尝试配置服务器
 */
async function try2ConfigServer() {
  const lastServer = Misc.getServer();
  const matched = Servers.find(item => item.name === lastServer);

  if (matched == undefined) {
    return;
  }

  const { tradeServer, quoteServer, restServer } = matched.servers;

  // mock场景不设置具体的服务器地址
  // const baseUrl = `http://${restServer.host}:${restServer.port}/quant/v3`;
  // HttpAssist.SetHttpBaseUrl(baseUrl);

  GlobalState.SetEnv(RuntimeEnvironment.WebSocket);
  await ServerManager.SetTradeServer(tradeServer.host, tradeServer.port);
  await ServerManager.SetQuoteServer(quoteServer.host, quoteServer.port);
}

// 检查是否已经登录，如果已经登录则连接webSocket
async function checkLogin() {
  const cached = Misc.getUser();
  if (cached) {
    // 本地缓存了用户信息，认作已登录状态
    return;
  }

  const localUser = Misc.getUser();
  if (localUser) {
    await LoginService.login(localUser.username, localUser.password, Mac, Os, false);
  }
}

const app = createApp(App);
app.use(ElementPlus, { locale: zhCn });
app.use(createPinia());
app.use(router);
app.mount('#app');

try2ConfigServer().then(() => checkLogin());

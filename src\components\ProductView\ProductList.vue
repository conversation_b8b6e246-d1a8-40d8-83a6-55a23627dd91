<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import TutorialPanel from '../ProductView/TutorialPanel.vue';
import { onMounted, shallowRef, useTemplateRef, inject, reactive } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import {
  getEmptyTableColumnConfig,
  type ColumnDefinition,
  type ProductInfo,
  type RowAction,
} from '@/types';
import { ProductService } from '@/api';
import { Formatter } from '@/script';
import { TABLE_COLUMN_SELECT_KEY } from '@/keys';

// 基础列定义
const columns: ColumnDefinition<ProductInfo> = [
  { key: 'fundName', title: '基金名字', width: 100, sortable: true },
  { key: 'id', title: '产品ID', width: 100, sortable: true },
  { key: 'orgId', title: '机构ID', width: 100, sortable: true },
  { key: 'orgName', title: '机构名称', width: 100, sortable: true },
  { key: 'fundCode', title: '基金外部编码', width: 110, sortable: true },
  { key: 'amacCode', title: '协会备案号', width: 100, sortable: true },
  { key: 'establishedDay', title: '基金成立日期', width: 110, sortable: true },
  { key: 'fundOrganization', title: '管理机构', width: 100, sortable: true },
  { key: 'fundManager', title: '基金经理', width: 100, sortable: true },
  { key: 'strategyType', title: '策略类型', width: 100, sortable: true },
  { key: 'subStrategyType', title: '二级策略类型', width: 110, sortable: true },
  { key: 'fundType', title: '基金类型', width: 100, sortable: true }, // （1真实，2虚拟，3组合）
  { key: 'basisReference', title: '业绩比较基准', width: 110, sortable: true },
  { key: 'closedFlag', title: '是否清盘', width: 100, sortable: true },
  { key: 'valuation', title: '估值方式', width: 100, sortable: true }, // （-1不估值，0T+0，1T+1）
  { key: 'riskCheck', title: '是否启用风控', width: 110, sortable: true },
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  { key: 'createUserId', title: '创建人ID', width: 100, sortable: true },
  { key: 'createUserName', title: '创建人名称', width: 110, sortable: true },
  { key: 'remark', title: '备注', width: 100, sortable: true },
];

// 行操作
const rowActions: RowAction<ProductInfo>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editProduct(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const tableCfgParams = inject(TABLE_COLUMN_SELECT_KEY, reactive(getEmptyTableColumnConfig()));
const panel = reactive({
  visible: false,
  contextProduct: null as ProductInfo | null,
});

function createProduct() {
  console.log('to create product');
  panel.visible = true;
  panel.contextProduct = null;
}

function editProduct(target: ProductInfo) {
  console.log('to edit product');
  panel.visible = true;
  panel.contextProduct = target;
}

function deleteRow(row: ProductInfo) {
  console.log('delete', row);
}

const records = shallowRef<ProductInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

function configColumn() {
  Object.assign(tableCfgParams, {
    name: '产品管理',
    columns: columns.map(x => ({ title: x!.title || '', datakey: x!.key })),
    selected: [],
    callback: (selected: string[]) => {
      console.log('selected columns', selected);
    },
  });
}

async function request() {
  records.value = await ProductService.getProducts();
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    searchPlaceholder="搜索产品"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button link size="small" class="typical-text-button" @click="configColumn">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button>
        <el-button type="primary" @click="createProduct">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建产品</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
  <el-dialog v-model="panel.visible" class="typical-dialog" title="产品配置" width="1198px">
    <TutorialPanel v-model="panel.contextProduct"></TutorialPanel>
  </el-dialog>
</template>

<style scoped></style>

import Utils from '../modules/utils';
import { BaseRepo } from '../modules/base-repo';
import { MomFundInfo } from '../types/table/fund';
import { MomAccount } from '../types/table/account';

export class GovernanceRepo extends BaseRepo {
    
    /**
     * 查询权限范围内能看到的所有产品
     */
    async QueryProducts() {
        return await this.assist.Get<MomFundInfo[]>('fund/list');
    }

    /**
     * 查询权限范围内能看到的所有策略（详情）
     */
    async QueryStrategies() {
        throw new Error('not implemented');
    }

    /**
     * 查询权限范围内能看到的所有账号（较简）
     */
    async QueryAccounts() {

        const resp = await this.assist.Get<MomAccount[]>('account/list');
        const { errorCode, errorMsg, data } = resp;
        
        if (errorCode === 0 && Array.isArray(data) && data.length >= 1) {
            
            const uniques = Utils.unique(data, x => x.id);
            resp.data = uniques;
        }
        
        return resp;
    }

    /**
     * 创建账号
     */
    async CreateAccount(account: MomAccount) {
        return await this.assist.Post<MomAccount[]>('account', {}, account);
    }

    /**
     * 更新账号
     */
    async UpdateAccount(account: MomAccount) {
        return await this.assist.Put<MomAccount[]>('account', {}, account);
    }

    /**
     * 删除账号
     */
    async DeleteAccount(account_id: string) {
        return await this.assist.Delete<[]>('account', { account_id });
    }

    /**
     * 连接账号（连接成功返回结果，为一个空数组）
     */
    async ConnectAccount(account_id: string) {
        return await this.assist.Get<[]>('account/connection_test', { account_id });
    }

    /**
     * 断开一个已连接的账号（连接成功返回结果，为一个空数组）
     */
    async DisconnectAccount(account_id: string) {
        return await this.assist.Put<[]>('account/logout', { account_id }, {});
    }
}
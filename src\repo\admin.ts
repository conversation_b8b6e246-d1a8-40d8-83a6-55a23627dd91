import { BaseRepo } from '../modules/base-repo';
import { SysMenu } from '../types/common';
import { FormRole, MomBroker, MomOrganization, MomRole, MomTerminal, MomUser } from '../types/table/admin';

export class AdminRepo extends BaseRepo {

    /**
     * 查询系统用户信息
     */
    async QueryUsers() {
        return await this.assist.Get<MomUser[]>('user/list');
    }

    /**
     * 查询系统机构列表
     */
    async QueryOrgs() {
        return await this.assist.Get<MomOrganization[]>('org/list');
    }

    /**
     * 创建系统机构
     */
    async CreateOrg(org: MomOrganization) {
        return await this.assist.Post<MomOrganization>('org', {}, org);
    }

    /**
     * 更新系统机构
     */
    async UpdateOrg(org: MomOrganization) {
        return await this.assist.Put<[]>('org', {}, org);
    }

    /**
     * 删除系统机构
     */
    async DeleteOrg(org_id: number) {
        return await this.assist.Delete<[]>('org', { org_id });
    }

    /**
     * 查询系统角色列表
     */
    async QueryRoles() {
        return await this.assist.Get<MomRole[]>('role/list');
    }

    /**
     * 创建系统角色
     */
    async CreateRole(role: FormRole) {
        return await this.assist.Post<MomRole>('role', {}, role);
    }

    /**
     * 更新系统角色
     */
    async UpdateRole(role: MomRole) {
        return await this.assist.Put<MomRole>('role', {}, role);
    }

    /**
     * 删除系统角色
     */
    async DeleteRole(role_id: number) {
        return await this.assist.Delete<[]>('role', { role_id });
    }

    /**
     * 查询系统菜单列表
     */
    async QueryMenus() {
        return await this.assist.Get<SysMenu[]>('menu');
    }

    /**
     * 查询系统券商列表
     */
    async QueryBrokers() {
        return await this.assist.Get<MomBroker[]>('broker/list');
    }

    /**
     * 查询交易终端列表
     */
    async QueryTerminals() {
        return await this.assist.Get<MomTerminal[]>('terminal/list');
    }

    /**
     * 查询当前归属的交易日
     */
    async QueryTradingDay() {
        return await this.assist.Get<string>('tradingDay');
    }
}
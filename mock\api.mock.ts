import { defineMock } from 'vite-plugin-mock-dev-server';
import { OrderInfo, PositionInfo, TradeRecordInfo } from '../../xtrade-sdk/dist';
import mockedAccountList from './mocked-account-list';
import mockedProductList from './mocked-product-list';
import mockedBrokerList from './mocked-broker-list';
import mockedOrgList from './mocked-org-list';
import mockRoleList from './mocked-role-list';
import mockedTerminalList from './mocked-terminal-list';
import mockedUserList from './mocked-user-list';
import mockedPositionList from './mocked-position-list';
import mockedOrderList from './mocked-order-list';
import mockedTradeList from './mocked-trade-list';
import mockedRiskTemplateList from './mocked-risk-template-list';
import mockedRiskIndicatorList from './mocked-risk-indicator-list';
import mockedRiskRuleList from './mocked-risk-rule-list';
import mockAssets from './mocked-asset-list';

// 模拟持仓数据
const mockPositions: PositionInfo[] = mockedPositionList;
// 模拟委托数据
const mockOrders: OrderInfo[] = mockedOrderList;
// 模拟成交数据
const mockTradeRecords: TradeRecordInfo[] = mockedTradeList;

export default defineMock([
  {
    url: '/api/login',
    method: 'POST',
    body: req => {
      return {
        errorCode: 0,
        data: {
          token: '1234567890',
          username: req.body.username,
          role: 'admin',
        },
      };
    },
  },
  {
    url: '/api/positions',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        errorCode: 0,
        data: mockPositions,
      };
    },
  },
  {
    url: '/api/orders',
    method: 'GET',
    body: req => {
      console.log(req.query);
      // 从mockOrders随机选择订单并生成指定count数量的数据
      const generateMockOrders = (count: number) => {
        return Array.from({ length: count }, () => {
          return {
            ...mockOrders[Math.floor(Math.random() * mockOrders.length)],
            id: Math.random(),
            orderPrice: Number((Math.random() * 100).toFixed(2)),
            orderTime: (() => {
              const date = new Date();
              date.setHours(Math.floor(Math.random() * 24));
              date.setMinutes(Math.floor(Math.random() * 60));
              date.setSeconds(Math.floor(Math.random() * 60));
              return date.toISOString().replace('T', ' ').slice(0, 19);
            })(),
          };
        });
      };
      return {
        errorCode: 0,
        data: generateMockOrders(1000),
      };
    },
  },
  {
    url: '/api/trades',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        errorCode: 0,
        data: mockTradeRecords,
      };
    },
  },
  {
    url: '/api/org/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedOrgList,
      };
    },
  },
  {
    url: '/api/role/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockRoleList(),
      };
    },
  },
  {
    url: '/api/role',
    method: 'POST',
    body: () => {
      return {
        errorCode: 0,
        data: {
          id: Math.floor(Math.random() * 1000),
        },
      };
    },
  },
  {
    url: '/api/role',
    method: 'PUT',
    body: () => {
      return {
        errorCode: 0,
      };
    },
  },
  {
    url: '/api/role',
    method: 'DELETE',
    body: () => {
      return {
        errorCode: 0,
      };
    },
  },
  {
    url: '/api/user/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedUserList,
      };
    },
  },

  {
    url: '/api/terminal/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedTerminalList,
      };
    },
  },
  {
    url: '/api/broker/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedBrokerList,
      };
    },
  },
  {
    url: '/api/account/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedAccountList,
      };
    },
  },
  {
    url: '/api/fund/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedProductList,
      };
    },
  },
  {
    url: '/api/risk-control/template/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedRiskTemplateList,
      };
    },
  },
  {
    url: '/api/risk-control/indicator/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedRiskIndicatorList,
      };
    },
  },
  {
    url: '/api/risk-control/rule/list',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockedRiskRuleList,
      };
    },
  },
  {
    url: '/api/risk-control/rule',
    method: 'DELETE',
    body: () => {
      return {
        errorCode: 0,
      };
    },
  },
  {
    url: '/api/market/asset',
    method: 'GET',
    body: req => {
      console.log(req);
      return {
        errorCode: 0,
        data: mockAssets(req.query.category),
      };
    },
  },
]);

<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, ref } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { Formatter } from '@/script';
import type { MomUser } from '../../../../xtrade-sdk/dist';
import { isUserDisabled, UserStatus } from '@/enum';

// 基础列定义
const columns: ColumnDefinition<MomUser> = [
  { key: 'id', title: '用户ID', width: 100, sortable: true },
  { key: 'userName', title: '用户名', width: 100, sortable: true },
  { key: 'fullName', title: '真实姓名', width: 100, sortable: true },
  // { key: 'password', title: '明文密码', width: 100, sortable: true },
  { key: 'firstLogin', title: '是否首次登录', width: 100, sortable: true },
  { key: 'email', title: '邮件', width: 200, sortable: true },
  { key: 'phoneNo', title: '电话号码', width: 100, sortable: true },
  { key: 'orgId', title: '机构ID', width: 100, sortable: true },
  { key: 'orgName', title: '机构名称', width: 150, sortable: true },
  { key: 'roleId', title: '角色ID', width: 100, sortable: true },
  { key: 'roleName', title: '角色名称', width: 100, sortable: true },
  {
    key: 'status',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomUser }) => {
      return (
        <el-switch
          modelValue={isUserDisabled(rowData) ? UserStatus.禁止 : UserStatus.正常}
          active-value={UserStatus.正常}
          inactive-value={UserStatus.禁止}
          before-change={() => beforeChange(rowData)}
        />
      );
    },
  },
  { key: 'errorNum', title: '密码错误次数', width: 100, sortable: true },
  {
    key: 'frozenExpireTime',
    title: '冻结过期时间',
    width: 130,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  { key: 'ip', title: '允许的IP地址列表', width: 200, sortable: true },
  { key: 'mac', title: '允许的MAC地址列表', width: 200, sortable: true },
  { key: 'diskNo', title: '磁盘序列号', width: 100, sortable: true }, // （预留）
  { key: 'cpuNo', title: 'CPU序列号', width: 100, sortable: true }, // （预留）
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  { key: 'deleteFlag', title: '删除标识', width: 100, sortable: true },
  { key: 'configuration', title: '用户配置', width: 100, sortable: true }, // （JSON格式）
  { key: 'multiLogin', title: '允许登录的终端数', width: 140, sortable: true },
];

// 行操作
const rowActions: RowAction<MomUser>[] = [
  {
    label: '设置',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '关联产品',
    icon: '',
    onClick: row => {
      linkProduct(row);
    },
  },
  {
    label: '在线查询',
    icon: '',
    onClick: row => {
      queryOnline(row);
    },
  },
  {
    label: '配置权限',
    icon: '',
    onClick: row => {
      configPermission(row);
    },
  },
  {
    label: '导入权限',
    icon: '',
    onClick: row => {
      importPermission(row);
    },
  },
  {
    label: '导出权限',
    icon: '',
    onClick: row => {
      exportPermission(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const records = ref<MomUser[]>([]);

function editRow(row: MomUser) {
  console.log('edit', row);
}

function linkProduct(row: MomUser) {
  console.log('link product', row);
}

function queryOnline(row: MomUser) {
  console.log('query online', row);
}

function configPermission(row: MomUser) {
  console.log('config permission', row);
}

function importPermission(row: MomUser) {
  console.log('import permission', row);
}

function exportPermission(row: MomUser) {
  console.log('export permission', row);
}

function deleteRow(row: MomUser) {
  console.log('delete', row);
}

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = await AdminService.getUsers();
}

async function beforeChange(row: MomUser) {
  console.log('before change', row);
  // TODO: 调接口修改用户状态
  row.status = isUserDisabled(row) ? UserStatus.正常 : UserStatus.禁止;
  return true;
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button>
        <el-button type="primary">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建用户</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>

import type { MomFundInfo } from '../../../xtrade-sdk/dist';

export type ProductInfo = MomFundInfo;

/** 产品详情信息 */
export interface ProductDetail {
  /** 总可用资金 */
  available: number;
  /** 总账户余额 */
  balance: number;
  /** 总平仓盈亏 */
  closeProfit: number;
  /** 总手续费 */
  commission: number;
  /** 总冻结手续费 */
  frozenCommission: number;
  /** 总冻结保证金 */
  frozenMargin: number;
  /** 关联的资金账户列表 */
  fundAccounts: any[];
  /** 基金唯一标识符 */
  fundId: string;
  /** 基金名称 */
  fundName: string;
  /** 累计入金金额（总计） */
  inMoney: number;
  /** 融资买入余额（总计） */
  loanBuyBalance: number;
  /** 融券卖出余额（总计） */
  loanSellBalance: number;
  /** 融券额度（总计） */
  loanSellQuota: number;
  /** 总保证金金额 */
  margin: number;
  /** 总持仓市值 */
  marketValue: number;
  /** 净值（最新净值） */
  nav: number;
  /** 实时净值 */
  navRealTime: number;
  /** 累计出金金额（总计） */
  outMoney: number;
  /** 总持仓盈亏 */
  positionProfit: number;
  /** 昨日总余额 */
  preBalance: number;
  /** 总涨跌幅百分比 */
  risePercent: number;
  /** 总可提现额度 */
  withdrawQuota: number;
}

export function MakeDefaultProductInfo() {
  const sample: ProductInfo = {
    id: 0,
    orgId: 0,
    orgName: '',
    fundName: '',
    amacCode: '',
    establishedDay: '',
    strategyType: '',
    fundType: 0,
    closedFlag: false,
    valuation: -1,
    riskCheck: false,
    updateTime: 0,
    fundCode: '',
    fundOrganization: '',
    fundManager: '',
    subStrategyType: '',
    basisReference: '',
    createTime: undefined,
    createUserId: undefined,
    createUserName: undefined,
    remark: undefined,
  };

  return sample;
}

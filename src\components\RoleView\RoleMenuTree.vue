<script setup lang="tsx">
import { computed, onMounted, ref, shallowRef } from 'vue';
import type { MomRole } from '../../../../xtrade-sdk/dist';
import { AdminService } from '@/api';
import type { SysMenuTree } from '@/types';

const { role } = defineProps<{
  role?: MomRole;
}>();

const data = shallowRef<SysMenuTree[]>([]);
const props = {
  value: 'value',
  label: 'menuName',
  children: 'children',
};

onMounted(() => {
  getFullMenuAndPermissions();
});

const getFullMenuAndPermissions = async () => {
  // data.value = await AdminService.getMenus()
  const example = [
    {
      id: 2300,
      userType: 1,
      menuName: '交易',
      parentMenuId: 0,
      sequence: 1,
      active: true,
      menuIcon: '',
      children: [
        {
          id: 2301,
          userType: 1,
          menuName: '普通交易',
          parentMenuId: 2300,
          sequence: 1,
          active: true,
          menuIcon: '',
          menListPermission: [
            {
              id: 1,
              userType: 1,
              permissionName: '下单',
              functionCode: 1001,
              url: '/api/test',
              method: 'GET',
              menuId: 2301,
              defaultPermission: true,
            },
            {
              id: 2,
              userType: 1,
              permissionName: '撤单',
              functionCode: 1001,
              url: '/api/test',
              method: 'GET',
              menuId: 2301,
              defaultPermission: true,
            },
          ],
        },
        {
          id: 2302,
          userType: 1,
          menuName: '批量交易',
          parentMenuId: 2300,
          sequence: 2,
          active: true,
          menuIcon: '',
          menListPermission: [
            {
              id: 3,
              userType: 1,
              permissionName: '下单',
              functionCode: 1001,
              url: '/api/test',
              method: 'GET',
              menuId: 2302,
              defaultPermission: true,
            },
          ],
        },
      ],
    },
  ];
};
</script>

<template>
  <ElTreeV2 :data :props></ElTreeV2>
</template>

<style scoped></style>

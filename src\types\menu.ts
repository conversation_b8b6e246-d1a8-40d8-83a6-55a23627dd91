import type { SysMenu } from '../../../xtrade-sdk/dist';
/**
 * 菜单项接口
 */
export interface Menu {
  name: string;
  component?: string;
  children?: Menu[];
  icon?: string;
}

/**
 * 菜单分组接口
 */
export interface MenuGroup {
  name: string;
  menus: Menu[];
}

/** 菜单树接口 */
export interface SysMenuTree {
  active: boolean;
  id: number;
  menuIcon: string;
  menuName: string;
  parentMenuId?: number;
  sequence: number;
  userType: number;
  children?: SysMenuTree[];
  menListPermission?: any[];
}

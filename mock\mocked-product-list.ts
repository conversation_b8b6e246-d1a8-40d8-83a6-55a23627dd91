const generateTestData = () => {
  const testData: any[] = [];

  for (let i = 1; i <= 100; i++) {
    testData.push({
      id: i,
      orgId: Math.floor(Math.random() * 1000) + 1,
      orgName: `机构名称-${i}`,
      fundName: `产品名称-${i}`,
      fundCode: `FUND${String(i).padStart(4, '0')}`,
      amacCode: `AMAC${String(i).padStart(6, '0')}`,
      establishedDay: `2023-01-${String((i % 28) + 1).padStart(2, '0')}`,
      fundOrganization: i % 2 === 0 ? `管理机构-${i}` : undefined,
      fundManager: i % 2 === 0 ? `基金经理-${i}` : undefined,
      strategyType: [`策略类型A`, `策略类型B`, `策略类型C`][i % 3],
      subStrategyType: i % 2 === 0 ? [`子策略X`, `子策略Y`, `子策略Z`][i % 3] : undefined,
      fundType: [1, 2, 3][i % 3],
      basisReference: i % 2 === 0 ? `基准参考-${i}` : undefined,
      closedFlag: i % 5 === 0,
      valuation: [-1, 0, 1][i % 3],
      riskCheck: i % 2 === 0,
      createTime: Date.now() - Math.round(Math.random() * 10000000),
      updateTime: Date.now() - Math.random() * 1000000,
      createUserId: i % 2 === 0 ? Math.floor(Math.random() * 100) + 1 : undefined,
      createUserName: i % 2 === 0 ? `创建者-${i}` : undefined,
      remark: i % 3 === 0 ? `备注信息-${i}` : undefined,
    });
  }

  return testData;
};

// Example usage
const momFundInfos = generateTestData();
export default momFundInfos;
